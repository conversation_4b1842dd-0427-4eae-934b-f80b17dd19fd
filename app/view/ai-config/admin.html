<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI配置管理 - 管理员界面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body class="bg-gray-100">
    <!-- 通知组件 -->
    <div id="notification" class="fixed top-4 right-4 z-50 hidden">
        <div class="bg-white border-l-4 border-blue-500 rounded-lg shadow-lg p-4 max-w-sm">
            <div class="flex">
                <div class="flex-shrink-0">
                    <div id="notification-icon" class="w-5 h-5"></div>
                </div>
                <div class="ml-3">
                    <p id="notification-message" class="text-sm text-gray-700"></p>
                </div>
                <div class="ml-auto pl-3">
                    <button onclick="hideNotification()" class="text-gray-400 hover:text-gray-600">
                        <span class="sr-only">关闭</span>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-4 py-8">
        <!-- 导航标签 -->
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800">AI配置管理 - 管理员界面</h1>
            <div class="flex space-x-2">
                <a href="/ai-config/enhanced" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                    用户界面
                </a>
            </div>
        </div>

        <!-- 标签导航 -->
        <div class="mb-8">
            <nav class="flex space-x-8" aria-label="Tabs">
                <button onclick="showTab('platforms')" id="tab-platforms" class="tab-button active">
                    AI平台管理
                </button>
                <button onclick="showTab('models')" id="tab-models" class="tab-button">
                    AI模型管理
                </button>
                <button onclick="showTab('configurations')" id="tab-configurations" class="tab-button">
                    配置管理
                </button>
            </nav>
        </div>

        <!-- 平台管理标签页 -->
        <div id="platforms-tab" class="tab-content">
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-700">AI平台管理</h2>
                    <button onclick="showCreatePlatformModal()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                        新增平台
                    </button>
                </div>
                <div id="platforms-list" class="space-y-4">
                    加载中...
                </div>
            </div>
        </div>

        <!-- 模型管理标签页 -->
        <div id="models-tab" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-700">AI模型管理</h2>
                    <div class="flex space-x-2">
                        <select id="platform-filter" class="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                            <option value="">所有平台</option>
                        </select>
                        <button onclick="showCreateModelModal()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                            新增模型
                        </button>
                    </div>
                </div>
                <div id="models-list" class="space-y-4">
                    加载中...
                </div>
            </div>
        </div>

        <!-- 配置管理标签页 -->
        <div id="configurations-tab" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-700">配置管理</h2>
                    <a href="/ai-config/enhanced" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                        前往配置管理
                    </a>
                </div>
                <div id="configurations-list" class="space-y-4">
                    加载中...
                </div>
            </div>
        </div>
    </div>

    <!-- 平台创建/编辑模态框 -->
    <div id="platform-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-40">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4 max-h-screen overflow-y-auto">
            <h3 id="platform-modal-title" class="text-lg font-semibold text-gray-800 mb-4">新增AI平台</h3>
            <form id="platform-form" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">平台名称</label>
                    <input type="text" id="platform-name" class="w-full border border-gray-300 rounded-lg px-3 py-2" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">平台代码</label>
                    <input type="text" id="platform-code" class="w-full border border-gray-300 rounded-lg px-3 py-2" required>
                    <p class="text-xs text-gray-500 mt-1">唯一标识符，如：openai、doubao等</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">API基础URL</label>
                    <input type="url" id="platform-api-url" class="w-full border border-gray-300 rounded-lg px-3 py-2" required>
                    <p class="text-xs text-gray-500 mt-1">如：https://api.openai.com/v1</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">平台描述</label>
                    <textarea id="platform-description" rows="3" class="w-full border border-gray-300 rounded-lg px-3 py-2"></textarea>
                </div>
                <div class="flex items-center">
                    <input type="checkbox" id="platform-is-active" class="mr-2" checked>
                    <label for="platform-is-active" class="text-sm text-gray-700">启用平台</label>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hidePlatformModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        保存
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 模型创建/编辑模态框 -->
    <div id="model-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-40">
        <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4 max-h-screen overflow-y-auto">
            <h3 id="model-modal-title" class="text-lg font-semibold text-gray-800 mb-4">新增AI模型</h3>
            <form id="model-form" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">所属平台</label>
                    <select id="model-platform-select" class="w-full border border-gray-300 rounded-lg px-3 py-2" required>
                        <option value="">选择平台</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">模型名称</label>
                    <input type="text" id="model-name" class="w-full border border-gray-300 rounded-lg px-3 py-2" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">模型代码</label>
                    <input type="text" id="model-code" class="w-full border border-gray-300 rounded-lg px-3 py-2" required>
                    <p class="text-xs text-gray-500 mt-1">API调用时使用的模型标识符</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">模型版本</label>
                    <input type="text" id="model-version" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">最大Token数</label>
                        <input type="number" id="model-max-tokens" min="1" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">每1K Token成本</label>
                        <input type="number" id="model-cost" step="0.000001" min="0" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">模型描述</label>
                    <textarea id="model-description" rows="3" class="w-full border border-gray-300 rounded-lg px-3 py-2"></textarea>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="model-supports-streaming" class="mr-2" checked>
                        <label for="model-supports-streaming" class="text-sm text-gray-700">支持流式输出</label>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="model-is-active" class="mr-2" checked>
                        <label for="model-is-active" class="text-sm text-gray-700">启用模型</label>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hideModelModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        保存
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let platforms = [];
        let models = [];
        let configurations = [];
        let currentTab = 'platforms';

        // 环境检测和API基础路径
        function getApiBasePath() {
            const currentPath = window.location.pathname;
            // 如果当前路径包含 /ai/，说明是生产环境
            if (currentPath.includes('/ai/')) {
                return '/ai/ai-config';
            }
            // 否则是测试环境
            return '/ai-config';
        }

        const API_BASE_PATH = getApiBasePath();

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadPlatforms();
            loadModels();
            loadConfigurations();
        });

        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const messageEl = document.getElementById('notification-message');
            const iconEl = document.getElementById('notification-icon');
            
            messageEl.textContent = message;
            
            const colors = {
                success: 'border-green-500',
                error: 'border-red-500',
                warning: 'border-yellow-500',
                info: 'border-blue-500'
            };
            
            const icons = {
                success: '✓',
                error: '✗',
                warning: '⚠',
                info: 'ℹ'
            };
            
            notification.className = `fixed top-4 right-4 z-50 ${colors[type] || colors.info}`;
            iconEl.textContent = icons[type] || icons.info;
            
            notification.classList.remove('hidden');
            
            setTimeout(() => {
                hideNotification();
            }, 3000);
        }

        function hideNotification() {
            document.getElementById('notification').classList.add('hidden');
        }

        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // 移除所有标签按钮的active状态
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tabName + '-tab').classList.remove('hidden');
            document.getElementById('tab-' + tabName).classList.add('active');
            
            currentTab = tabName;
        }

        // ==================== 平台管理 ====================

        // 加载平台列表
        async function loadPlatforms() {
            try {
                const response = await axios.get(`${API_BASE_PATH}/platforms`);
                platforms = response.data.data;
                renderPlatforms();
                populatePlatformSelects();
            } catch (error) {
                console.error('加载平台列表失败:', error);
                showNotification('加载平台列表失败', 'error');
            }
        }

        // 渲染平台列表
        function renderPlatforms() {
            const platformsList = document.getElementById('platforms-list');
            if (platforms.length === 0) {
                platformsList.innerHTML = '<p class="text-gray-500">暂无平台</p>';
                return;
            }

            platformsList.innerHTML = platforms.map(platform => `
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <span class="font-medium">${platform.platform_name}</span>
                            <span class="text-sm text-gray-500">(${platform.platform_code})</span>
                            ${!platform.is_active ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">已停用</span>' : ''}
                        </div>
                        <div class="text-sm text-gray-500 mt-1">
                            ${platform.api_base_url}
                        </div>
                        ${platform.description ? `<div class="text-xs text-gray-400 mt-1">${platform.description}</div>` : ''}
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="editPlatform(${platform.platform_id})" class="text-yellow-500 hover:text-yellow-600 text-sm">编辑</button>
                        <button onclick="deletePlatform(${platform.platform_id})" class="text-red-500 hover:text-red-600 text-sm">删除</button>
                    </div>
                </div>
            `).join('');
        }

        // 显示创建平台模态框
        function showCreatePlatformModal() {
            document.getElementById('platform-modal-title').textContent = '新增AI平台';
            document.getElementById('platform-form').reset();
            document.getElementById('platform-form').dataset.mode = 'create';
            delete document.getElementById('platform-form').dataset.platformId;
            document.getElementById('platform-modal').classList.remove('hidden');
            document.getElementById('platform-modal').classList.add('flex');
        }

        // 隐藏平台模态框
        function hidePlatformModal() {
            document.getElementById('platform-modal').classList.add('hidden');
            document.getElementById('platform-modal').classList.remove('flex');
        }

        // 编辑平台
        async function editPlatform(platformId) {
            try {
                const response = await axios.get(`${API_BASE_PATH}/platform?platform_id=${platformId}`);
                const platform = response.data.data;

                document.getElementById('platform-modal-title').textContent = '编辑AI平台';
                document.getElementById('platform-name').value = platform.platform_name;
                document.getElementById('platform-code').value = platform.platform_code;
                document.getElementById('platform-api-url').value = platform.api_base_url;
                document.getElementById('platform-description').value = platform.description || '';
                document.getElementById('platform-is-active').checked = platform.is_active;

                document.getElementById('platform-form').dataset.mode = 'edit';
                document.getElementById('platform-form').dataset.platformId = platformId;

                document.getElementById('platform-modal').classList.remove('hidden');
                document.getElementById('platform-modal').classList.add('flex');
            } catch (error) {
                console.error('获取平台详情失败:', error);
                showNotification('获取平台详情失败', 'error');
            }
        }

        // 删除平台
        async function deletePlatform(platformId) {
            if (!confirm('确定要删除这个平台吗？删除后相关的模型和配置也会受到影响。')) {
                return;
            }

            try {
                await axios.delete(`${API_BASE_PATH}/platforms?platform_id=${platformId}`);
                loadPlatforms();
                loadModels(); // 重新加载模型列表
                showNotification('平台删除成功！', 'success');
            } catch (error) {
                console.error('删除平台失败:', error);
                showNotification('删除平台失败: ' + (error.response?.data?.message || error.message), 'error');
            }
        }

        // 平台表单提交
        document.getElementById('platform-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const mode = this.dataset.mode;
            const platformId = this.dataset.platformId;

            const formData = {
                platform_name: document.getElementById('platform-name').value,
                platform_code: document.getElementById('platform-code').value,
                api_base_url: document.getElementById('platform-api-url').value,
                description: document.getElementById('platform-description').value,
                is_active: document.getElementById('platform-is-active').checked ? 1 : 0
            };

            try {
                if (mode === 'edit') {
                    await axios.put(`${API_BASE_PATH}/platforms?platform_id=${platformId}`, formData);
                    showNotification('平台更新成功！', 'success');
                } else {
                    await axios.post('${API_BASE_PATH}/platforms', formData);
                    showNotification('平台创建成功！', 'success');
                }

                hidePlatformModal();
                loadPlatforms();
            } catch (error) {
                console.error('保存平台失败:', error);
                showNotification('保存平台失败: ' + (error.response?.data?.message || error.message), 'error');
            }
        });

        // 填充平台选择框
        function populatePlatformSelects() {
            const selects = ['model-platform-select', 'platform-filter'];
            selects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (select) {
                    const currentValue = select.value;
                    if (selectId === 'platform-filter') {
                        select.innerHTML = '<option value="">所有平台</option>' +
                            platforms.map(platform => `<option value="${platform.platform_id}">${platform.platform_name}</option>`).join('');
                    } else {
                        select.innerHTML = '<option value="">选择平台</option>' +
                            platforms.map(platform => `<option value="${platform.platform_id}">${platform.platform_name}</option>`).join('');
                    }
                    if (currentValue) select.value = currentValue;
                }
            });
        }

        // ==================== 模型管理 ====================

        // 加载模型列表
        async function loadModels() {
            try {
                const response = await axios.get('${API_BASE_PATH}/all-models');
                models = response.data.data;
                renderModels();
            } catch (error) {
                console.error('加载模型列表失败:', error);
                showNotification('加载模型列表失败', 'error');
            }
        }

        // 渲染模型列表
        function renderModels() {
            const modelsList = document.getElementById('models-list');
            const platformFilter = document.getElementById('platform-filter').value;

            let filteredModels = models;
            if (platformFilter) {
                filteredModels = models.filter(model => model.platform_id == platformFilter);
            }

            if (filteredModels.length === 0) {
                modelsList.innerHTML = '<p class="text-gray-500">暂无模型</p>';
                return;
            }

            modelsList.innerHTML = filteredModels.map(model => `
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <span class="font-medium">${model.model_name}</span>
                            <span class="text-sm text-gray-500">(${model.model_code})</span>
                            ${!model.is_active ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">已停用</span>' : ''}
                        </div>
                        <div class="text-sm text-gray-500 mt-1">
                            平台: ${model.platform?.platform_name} |
                            最大Token: ${model.max_tokens || 'N/A'} |
                            流式输出: ${model.supports_streaming ? '支持' : '不支持'}
                        </div>
                        ${model.description ? `<div class="text-xs text-gray-400 mt-1">${model.description}</div>` : ''}
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="editModel(${model.model_id})" class="text-yellow-500 hover:text-yellow-600 text-sm">编辑</button>
                        <button onclick="deleteModel(${model.model_id})" class="text-red-500 hover:text-red-600 text-sm">删除</button>
                    </div>
                </div>
            `).join('');
        }

        // 平台过滤器变化
        document.getElementById('platform-filter').addEventListener('change', function() {
            renderModels();
        });

        // 显示创建模型模态框
        function showCreateModelModal() {
            document.getElementById('model-modal-title').textContent = '新增AI模型';
            document.getElementById('model-form').reset();
            document.getElementById('model-form').dataset.mode = 'create';
            delete document.getElementById('model-form').dataset.modelId;
            document.getElementById('model-modal').classList.remove('hidden');
            document.getElementById('model-modal').classList.add('flex');
        }

        // 隐藏模型模态框
        function hideModelModal() {
            document.getElementById('model-modal').classList.add('hidden');
            document.getElementById('model-modal').classList.remove('flex');
        }

        // 编辑模型
        async function editModel(modelId) {
            try {
                const response = await axios.get(`${API_BASE_PATH}/model?model_id=${modelId}`);
                const model = response.data.data;

                document.getElementById('model-modal-title').textContent = '编辑AI模型';
                document.getElementById('model-platform-select').value = model.platform_id;
                document.getElementById('model-name').value = model.model_name;
                document.getElementById('model-code').value = model.model_code;
                document.getElementById('model-version').value = model.model_version || '';
                document.getElementById('model-max-tokens').value = model.max_tokens || '';
                document.getElementById('model-cost').value = model.cost_per_1k_tokens || '';
                document.getElementById('model-description').value = model.description || '';
                document.getElementById('model-supports-streaming').checked = model.supports_streaming;
                document.getElementById('model-is-active').checked = model.is_active;

                document.getElementById('model-form').dataset.mode = 'edit';
                document.getElementById('model-form').dataset.modelId = modelId;

                document.getElementById('model-modal').classList.remove('hidden');
                document.getElementById('model-modal').classList.add('flex');
            } catch (error) {
                console.error('获取模型详情失败:', error);
                showNotification('获取模型详情失败', 'error');
            }
        }

        // 删除模型
        async function deleteModel(modelId) {
            if (!confirm('确定要删除这个模型吗？删除后相关的配置也会受到影响。')) {
                return;
            }

            try {
                await axios.delete(`${API_BASE_PATH}/models?model_id=${modelId}`);
                loadModels();
                showNotification('模型删除成功！', 'success');
            } catch (error) {
                console.error('删除模型失败:', error);
                showNotification('删除模型失败: ' + (error.response?.data?.message || error.message), 'error');
            }
        }

        // 模型表单提交
        document.getElementById('model-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const mode = this.dataset.mode;
            const modelId = this.dataset.modelId;

            const formData = {
                platform_id: document.getElementById('model-platform-select').value,
                model_name: document.getElementById('model-name').value,
                model_code: document.getElementById('model-code').value,
                model_version: document.getElementById('model-version').value,
                max_tokens: document.getElementById('model-max-tokens').value ? parseInt(document.getElementById('model-max-tokens').value) : null,
                cost_per_1k_tokens: document.getElementById('model-cost').value ? parseFloat(document.getElementById('model-cost').value) : null,
                description: document.getElementById('model-description').value,
                supports_streaming: document.getElementById('model-supports-streaming').checked ? 1 : 0,
                is_active: document.getElementById('model-is-active').checked ? 1 : 0
            };

            try {
                if (mode === 'edit') {
                    await axios.put(`${API_BASE_PATH}/models?model_id=${modelId}`, formData);
                    showNotification('模型更新成功！', 'success');
                } else {
                    await axios.post('${API_BASE_PATH}/models', formData);
                    showNotification('模型创建成功！', 'success');
                }

                hideModelModal();
                loadModels();
            } catch (error) {
                console.error('保存模型失败:', error);
                showNotification('保存模型失败: ' + (error.response?.data?.message || error.message), 'error');
            }
        });

        // ==================== 配置管理 ====================

        // 加载配置列表
        async function loadConfigurations() {
            try {
                const response = await axios.get('${API_BASE_PATH}/configurations');
                configurations = response.data.data;
                renderConfigurations();
            } catch (error) {
                console.error('加载配置列表失败:', error);
                showNotification('加载配置列表失败', 'error');
            }
        }

        // 渲染配置列表
        function renderConfigurations() {
            const configsList = document.getElementById('configurations-list');
            if (configurations.length === 0) {
                configsList.innerHTML = '<p class="text-gray-500">暂无配置</p>';
                return;
            }

            configsList.innerHTML = configurations.map(config => `
                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2">
                            <span class="font-medium">${config.config_name}</span>
                            ${config.is_default ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">默认</span>' : ''}
                            ${!config.is_active ? '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">已停用</span>' : ''}
                        </div>
                        <div class="text-sm text-gray-500 mt-1">
                            ${config.platform?.platform_name} - ${config.model?.model_name}
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <a href="/ai-config/enhanced" class="text-blue-500 hover:text-blue-600 text-sm">管理</a>
                    </div>
                </div>
            `).join('');
        }
    </script>

    <style>
        .tab-button {
            @apply py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300;
        }
        .tab-button.active {
            @apply border-blue-500 text-blue-600;
        }
    </style>
</body>
</html>
