-- AI平台表
CREATE TABLE `ai_platforms` (
  `platform_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '平台ID',
  `platform_name` varchar(50) NOT NULL COMMENT '平台名称',
  `platform_code` varchar(20) NOT NULL COMMENT '平台代码',
  `api_base_url` varchar(255) DEFAULT NULL COMMENT 'API基础URL',
  `description` text COMMENT '平台描述',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`platform_id`),
  UNIQUE KEY `uk_platform_code` (`platform_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI平台表';

-- AI模型表
CREATE TABLE `ai_models` (
  `model_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '模型ID',
  `platform_id` int(11) NOT NULL COMMENT '所属平台ID',
  `model_name` varchar(100) NOT NULL COMMENT '模型名称',
  `model_code` varchar(50) NOT NULL COMMENT '模型代码',
  `model_version` varchar(20) DEFAULT NULL COMMENT '模型版本',
  `max_tokens` int(11) DEFAULT NULL COMMENT '最大token数',
  `supports_streaming` tinyint(1) DEFAULT 1 COMMENT '是否支持流式输出',
  `cost_per_1k_tokens` decimal(10,6) DEFAULT NULL COMMENT '每1K token成本',
  `description` text COMMENT '模型描述',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`model_id`),
  UNIQUE KEY `uk_platform_model` (`platform_id`, `model_code`),
  KEY `idx_platform_id` (`platform_id`),
  CONSTRAINT `fk_ai_models_platform` FOREIGN KEY (`platform_id`) REFERENCES `ai_platforms` (`platform_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI模型表';

-- AI配置表
CREATE TABLE `ai_configurations` (
  `config_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_name` varchar(50) NOT NULL COMMENT '配置名称',
  `platform_id` int(11) NOT NULL COMMENT '平台ID',
  `model_id` int(11) NOT NULL COMMENT '模型ID',
  `api_key` varchar(255) NOT NULL COMMENT 'API密钥',
  `api_endpoint` varchar(255) DEFAULT NULL COMMENT 'API端点',
  `temperature` decimal(3,2) DEFAULT 0.80 COMMENT '温度参数',
  `max_tokens` int(11) DEFAULT NULL COMMENT '最大输出token数',
  `frequency_penalty` decimal(3,2) DEFAULT 0.00 COMMENT '频率惩罚',
  `presence_penalty` decimal(3,2) DEFAULT 0.00 COMMENT '存在惩罚',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否为默认配置',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`config_id`),
  UNIQUE KEY `uk_config_name` (`config_name`),
  KEY `idx_platform_id` (`platform_id`),
  KEY `idx_model_id` (`model_id`),
  KEY `idx_is_default` (`is_default`),
  CONSTRAINT `fk_ai_configurations_platform` FOREIGN KEY (`platform_id`) REFERENCES `ai_platforms` (`platform_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_ai_configurations_model` FOREIGN KEY (`model_id`) REFERENCES `ai_models` (`model_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI配置表';

-- 插入初始平台数据
INSERT INTO `ai_platforms` (`platform_name`, `platform_code`, `api_base_url`, `description`, `is_active`) VALUES
('字节跳动豆包', 'doubao', 'https://ark.cn-beijing.volces.com/api/v3', '字节跳动旗下的AI大模型平台', 1),
('硅基流动', 'siliconflow', 'https://api.siliconflow.cn/v1', '硅基流动AI模型服务平台', 1),
('OpenAI', 'openai', 'https://api.openai.com/v1', 'OpenAI官方API服务', 1),
('Azure OpenAI', 'azure-openai', NULL, 'Microsoft Azure OpenAI服务', 1),
('百度千帆', 'qianfan', 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1', '百度千帆大模型平台', 1),
('阿里云通义千问', 'dashscope', 'https://dashscope.aliyuncs.com/api/v1', '阿里云通义千问大模型', 1);

-- 插入初始模型数据
INSERT INTO `ai_models` (`platform_id`, `model_name`, `model_code`, `model_version`, `max_tokens`, `supports_streaming`, `description`, `is_active`) VALUES
-- 豆包模型
(1, 'Doubao-1.5-pro-32k', 'doubao-1-5-pro-32k-250115', '1.5', 32000, 1, '豆包1.5 Pro版本，支持32K上下文', 1),
(1, 'Doubao-1.5-lite-4k', 'doubao-1-5-lite-4k', '1.5', 4000, 1, '豆包1.5 Lite版本，支持4K上下文', 1),

-- 硅基流动模型
(2, 'DeepSeek-V2.5', 'deepseek-ai/DeepSeek-V2.5', '2.5', 32000, 1, 'DeepSeek V2.5大模型', 1),
(2, 'Qwen2.5-72B-Instruct', 'Qwen/Qwen2.5-72B-Instruct', '2.5', 32000, 1, '通义千问2.5-72B指令模型', 1),
(2, 'ChatGLM3-6B', 'THUDM/chatglm3-6b', '3.0', 8000, 1, 'ChatGLM3-6B对话模型', 1),

-- OpenAI模型
(3, 'GPT-4o', 'gpt-4o', '4.0', 128000, 1, 'GPT-4o最新版本', 1),
(3, 'GPT-4o-mini', 'gpt-4o-mini', '4.0', 128000, 1, 'GPT-4o mini版本', 1),
(3, 'GPT-3.5-turbo', 'gpt-3.5-turbo', '3.5', 16000, 1, 'GPT-3.5 Turbo模型', 1);

-- 插入当前.env中的配置作为默认配置
INSERT INTO `ai_configurations` (`config_name`, `platform_id`, `model_id`, `api_key`, `api_endpoint`, `temperature`, `is_default`, `is_active`) VALUES
('默认豆包配置', 1, 1, '58481c24-6f09-47b2-b221-388bacd91845', 'https://ark.cn-beijing.volces.com/api/v3/chat/completions', 1.00, 1, 1);
